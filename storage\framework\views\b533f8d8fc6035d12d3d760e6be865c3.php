<div class="container mt-4">
    <!-- Navigation Menu -->
    <ul class="nav nav-pills mb-3">
        <li class="nav-item">
            <a class="nav-link page_navigation_nav_link <?php echo e(request()->is('admin/event-detail*') ? 'active' : ''); ?>"
                href="<?php echo e(route('panel.event_detail', ['event_id' => $event->id])); ?>">Event Overview</a>
        </li>
        <?php if($event->status != 2): ?>
            <li class="nav-item">
                <a class="nav-link page_navigation_nav_link <?php echo e(request()->is('*/register-list') ? 'active' : ''); ?>"
                    href="<?php echo e(url('admin/event/' . $event->id . '/register-list')); ?>">Registration List</a>
            </li>
            <?php if(isset($event) && $event->attendees_info): ?>
                <li class="nav-item">
                    <a class="nav-link page_navigation_nav_link <?php echo e(request()->is('*/attendees-info') ? 'active' : ''); ?>"
                        href="<?php echo e(route('panel.attendees_info', ['event_id' => $event->id])); ?>">Attendees Info</a>
                </li>
            <?php endif; ?>
        <?php endif; ?>
        <?php if(isset($event) && $event->user_check_in && $event->status != 2): ?>
            <li class="nav-item">
                <a class="nav-link page_navigation_nav_link <?php echo e(request()->is('*/checkin') ? 'active' : ''); ?>"
                    href="<?php echo e(route('panel.event_manuelcheckin__or__qrcheckin', ['event_id' => $event->id])); ?>">
                    Event Check-in
                </a>
            </li>
        <?php endif; ?>
        <?php if($event->status != 2): ?>
        <li class="nav-item">
            <a class="nav-link <?php if(!$event->badge_creation): ?> passive <?php endif; ?> page_navigation_nav_link <?php echo e(request()->is('*/badge-creation', '*/badge-creation/*') ? 'active' : ''); ?>"
                href="<?php if($event->badge_creation): ?><?php echo e(route('panel.badge_creation', ['event_id' => $event->id]) . '?design=template'); ?><?php else: ?>#<?php endif; ?>">Design Your Badge</a>
        </li>
        
        <?php endif; ?>
        <?php if($event->status != 2): ?>
            <li class="nav-item">
                <a class="nav-link page_navigation_nav_link <?php echo e(request()->is('*/badge-list') ? 'active' : ''); ?>"
                    href="<?php echo e(route('panel.badge_list', ['event_id' => $event->id])); ?>">Badge List</a>
            </li>
            <li class="nav-item">
                <a class="nav-link passive page_navigation_nav_link" href="#">Interactive Tools</a>
            </li>
            <li class="nav-item">
                <a class="nav-link passive page_navigation_nav_link" href="#">Feedback Survey</a>
            </li>
        <?php endif; ?>
    </ul>
<?php /**PATH C:\laragon\www\bd-qr-badge\resources\views/layouts/page_navigation.blade.php ENDPATH**/ ?>