<?php $__env->startSection('content'); ?>

    <link rel="stylesheet" href="https://cdn.datatables.net/2.1.8/css/dataTables.dataTables.min.css" />

    <style>
        .dt-input {
            margin-right: 10px;
        }
    </style>

    <div class="checkout">
        <div class="card shadow-none border">
            <div class="card-body p-4">
                <?php echo $__env->make('layouts.event_details', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                <?php echo $__env->make('layouts.page_navigation', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                
                <div class="row mt-5" id="uploadDesign">
                    <!-- Upload Section -->
                    <div class="col-md-8" style="overflow-x: auto;">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 class="fw-bolder mb-0"></h5>
                            <div class="d-flex gap-2">
                                <a href="<?php echo e(route('panel.badge_creation_manual', ['event_id' => $event->id])); ?>"
                                   class="btn btn-primary">
                                    <i class="ti ti-user-plus"></i> Manual Badge
                                </a>
                                <button type="button" class="btn btn-success" id="bulk-badge-generate-btn">
                                    <i class="ti ti-plus"></i> Generate All Badges
                                </button>
                            </div>
                        </div>

                        <table class="table table-bordered table-hover" id="badge_list_table">
                            <thead>
                                <tr>
                                    <th scope="col">Name Surname</th>
                                    <th scope="col">Country</th>
                                    <th scope="col">Job Title</th>
                                    <th scope="col">Email - UserId</th>
                                    <th scope="col">Action</th>
                                </tr>
                            </thead>
                            <tbody>
                            <?php $__currentLoopData = $badges; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $badge): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td>
                                        <?php echo e($badge->first_name . ' ' . $badge->last_name); ?>

                                        <?php if($badge->register_status == 3): ?>
                                            <span class="badge bg-warning text-dark ms-1" title="Manual/External Badge">
                                                <i class="ti ti-user-plus"></i> Manual
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo e($badge->country ?? '-'); ?></td>
                                    <td><?php echo e($badge->department ?? '-'); ?></td>
                                    <td>
                                        <span class="mb-1 badge font-medium bg-light-info text-info" style="font-size: 10px"><?php echo e($badge->email ?? '-'); ?></span><br>
                                        <span class="mb-1 badge font-medium bg-light-info text-info" style="font-size: 10px"><?php echo e($badge->user_id ?? '-'); ?></span>
                                        <?php if($badge->register_status == 3): ?>
                                            <br><span class="badge bg-light-warning text-warning" style="font-size: 9px">External Participant</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="#" data-badge-id="<?php echo e($badge->id); ?>" class="btn btn-primary btn-sm badge_preview_btn" title="Preview"><i class="ti ti-eye"></i></a>
                                            <a href="<?php echo e(route('panel.badge_edit', ['event_id' => $event->id, 'id' => $badge->id])); ?>" class="btn btn-warning btn-sm" title="Edit"><i class="ti ti-edit"></i></a>
                                            <a href="<?php echo e(route('panel.badge_download', ['id' => $badge->id, 'event_id' => $event->id ])); ?>" class="btn btn-success btn-sm" title="Download"><i class="ti ti-download"></i></a>
                                            <a href="<?php echo e(route('panel.badge_print_page', ['id' => $badge->id, 'event_id' => $event->id ])); ?>" class="btn btn-light btn-sm" target="_blank" title="Print"><i class="ti ti-printer"></i></a>
                                            <a href="#" data-url="<?php echo e(route('panel.badge_delete', ['id' => $badge->id, 'event_id' => $event->id ])); ?>" class="btn btn-danger btn-sm delete-badge-btn" title="Delete"><i class="ti ti-trash"></i></a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>

                    </div>

                    <!-- Form Section -->
                    <div class="col-md-4">
                        <p class="fw-bolder">Preview</p>
                        <div
                            class="preview-container position-relative d-flex flex-column justify-content-center align-items-center text-center mt-2">
                            
                            
                            <h3 class="first_name fw-bolder">JOHN</h3>
                            <h4 class="last_name fw-bolder">DOE</h4>
                            <p class="company_name fw-bolder">Country</p>

                            
                        </div>
                    </div>
                </div>

            </div>
        </div>

    </div>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
    <!-- ---------------------------------------------- -->
    <!-- current page js files -->
    <!-- ---------------------------------------------- -->
    <script src="<?php echo e(asset('js/uploadQRDesign__TemplateQRDesign.js')); ?>?v=<?php echo e(now()->format('YmdHi')); ?>"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://cdn.datatables.net/2.1.8/js/dataTables.min.js"></script>

    <script>
        $(document).ready(function() {
            // Initialize DataTable
            $('#badge_list_table').DataTable({
                "pageLength": 25,
                "lengthChange": false, // Hide "Show X entries per page"
                "ordering": false, // Disable sorting for all columns
                "columnDefs": [
                    {
                        "orderable": false,
                        "targets": "_all" // Disable sorting for all columns
                    }
                ],
                "language": {
                    "search": "Search badges:",
                    "info": "Showing _START_ to _END_ of _TOTAL_ badges",
                    "infoEmpty": "No badges found",
                    "infoFiltered": "(filtered from _MAX_ total badges)",
                    "paginate": {
                        "first": '<i class="ti ti-chevrons-left"></i>',
                        "last": '<i class="ti ti-chevrons-right"></i>',
                        "next": '<i class="ti ti-chevron-right"></i>',
                        "previous": '<i class="ti ti-chevron-left"></i>'
                    }
                }
            });
            $('.badge_preview_btn').click(function(e) {
                e.preventDefault();

                var badgeId = $(this).data('badge-id');

                var url = '<?php echo e(route('panel.badge_preview', ['event_id' => $event->id, 'badge_id' => ':badge_id'])); ?>';
                url = url.replace(':badge_id', badgeId);

                $.ajax({
                    url: url,
                    type: 'GET',
                    success: function(data) {
                        $('.preview-container').html("");
                        $('.preview-container').css({
                            'background': 'url(/images/badge/generated/' + data.badge.badge_path + ') no-repeat center center',
                        });
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        console.log(textStatus, errorThrown);
                    }
                });
            });
        });

        $(document).ready(function() {
            $('.delete-badge-btn').click(function(e) {
                e.preventDefault();
                var url = $(this).data('url');

                Swal.fire({
                    title: 'Are you sure?',
                    text: "You won't be able to revert this!",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Yes, delete it!'
                }).then((result) => {
                    if (result.isConfirmed) {
                        window.location.href = url;
                    }
                });
            });

            // Sayfa yüklendiğinde template kontrolü yap
            checkTemplateStatus();
        });

        function checkTemplateStatus() {
            const bulkBtn = document.getElementById('bulk-badge-generate-btn');

            fetch(`<?php echo e(route('panel.badge_template_check', ['event_id' => $event->id])); ?>`)
                .then(response => response.json())
                .then(data => {
                    if (!data.has_template) {
                        bulkBtn.disabled = true;
                        bulkBtn.classList.add('disabled');
                        bulkBtn.style.opacity = '0.5';
                        bulkBtn.title = 'Please create a badge template first';
                        bulkBtn.innerHTML = '<i class="ti ti-alert-circle"></i> Template Required';
                    } else {
                        bulkBtn.disabled = false;
                        bulkBtn.classList.remove('disabled');
                        bulkBtn.style.opacity = '1';
                        bulkBtn.title = 'Generate badges for all shortlisted users';
                        bulkBtn.innerHTML = '<i class="ti ti-plus"></i> Generate All Badges';
                    }
                })
                .catch(error => {
                    console.error('Template check failed:', error);
                });
        }

        // Toplu badge oluşturma
        document.getElementById('bulk-badge-generate-btn').addEventListener('click', function() {
            // Önce template kontrolü yap
            fetch(`<?php echo e(route('panel.badge_template_check', ['event_id' => $event->id])); ?>`)
                .then(response => response.json())
                .then(data => {
                    if (!data.has_template) {
                        Swal.fire({
                            title: 'Template Required',
                            text: data.message || 'Please create a badge template first before generating badges.',
                            icon: 'warning',
                            confirmButtonText: 'Go to Badge Creation',
                            showCancelButton: true,
                            cancelButtonText: 'Cancel'
                        }).then((result) => {
                            if (result.isConfirmed) {
                                window.location.href = "<?php echo e(route('panel.badge_creation', ['event_id' => $event->id])); ?>";
                            }
                        });
                        return;
                    }

                    // Template varsa badge oluşturmaya devam et
                    Swal.fire({
                        title: 'Generate All Badges?',
                        text: "This will create badges for all users in the shortlist. Existing badges will be skipped.",
                        icon: 'question',
                        showCancelButton: true,
                        confirmButtonColor: '#3085d6',
                        cancelButtonColor: '#d33',
                        confirmButtonText: 'Yes, generate all!',
                        showLoaderOnConfirm: true,
                        preConfirm: () => {
                            // Form oluştur ve gönder
                            const form = document.createElement('form');
                            form.method = 'POST';
                            form.action = "<?php echo e(route('panel.badge_generate_bulk', ['event_id' => $event->id])); ?>";

                            // CSRF token
                            const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
                            const csrfInput = document.createElement('input');
                            csrfInput.type = 'hidden';
                            csrfInput.name = '_token';
                            csrfInput.value = csrfToken;
                            form.appendChild(csrfInput);

                            // Template ID (varsayılan 1)
                            const templateInput = document.createElement('input');
                            templateInput.type = 'hidden';
                            templateInput.name = 'template_id';
                            templateInput.value = '1';
                            form.appendChild(templateInput);

                            document.body.appendChild(form);
                            form.submit();
                        },
                        allowOutsideClick: () => !Swal.isLoading()
                    });
                })
                .catch(error => {
                    console.error('Template check failed:', error);
                    Swal.fire({
                        title: 'Error',
                        text: 'Failed to check template. Please try again.',
                        icon: 'error'
                    });
                });
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\bd-qr-badge\resources\views/event/badge_list.blade.php ENDPATH**/ ?>