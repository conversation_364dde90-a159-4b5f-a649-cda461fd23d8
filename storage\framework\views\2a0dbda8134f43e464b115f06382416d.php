<div class="event_details_container row">
    <div class="col-12 d-flex justify-content-between align-items-center flex-wrap">
        <div>
            <h5 class="mb-0 event_title fw-bolder">
                <?php echo e((request()->has('clone') ? 'Clone Event' : (request()->is('admin/new-event') ? 'Add Event' : (request()->is('admin/event-detail/*') ? '' : 'Event Details')))); ?>

            </h5>
            <?php if(request()->is('admin/new-event')): ?>
                <p class="mb-3">
                    <small class="text-muted">
                        Only BD-organized or BD-focused events can be added as an Event. Events where B<PERSON> is not the main organizer are not supported.
                    </small>
                </p>
            <?php endif; ?>
        </div>
        <div class="d-flex flex-row align-items-center">
            <p class="pe-3 mb-0">Status: </p>
            <div class="btn-group">

                    <?php if(request()->is('admin/event-detail/*') || request()->is('admin/event/*')): ?>
                        <?php if($event->status == 2): ?>
                            <button
                                class="btn status dropdown-toggle pass"
                                type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                Draft
                            </button>
                        <?php else: ?>
                            <?php if($event->event_dates()->orderBy('event_date', 'ASC')->first() && $event->event_dates()->orderBy('event_date', 'ASC')->first()->event_date >= date('Y-m-d')): ?>
                                <button
                                    class="btn status dropdown-toggle active"
                                    type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    Active
                                </button>
                            <?php else: ?>
                                <button
                                    class="btn status dropdown-toggle pass"
                                    type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    Past
                                </button>
                            <?php endif; ?>
                        <?php endif; ?>
                    <?php else: ?>
                        <button
                            class="btn status dropdown-toggle active"
                            type="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Active
                        </button>
                    <?php endif; ?>

            </div>
        </div>
    </div>

    <?php if(request()->is('admin/event-detail/*') || request()->is('admin/new-event')): ?>
        <div class="col-12">
            <div class="d-flex flex-wrap gap-3">
                <?php
                    $header_toggles = [
                        'User Check-in' => 'user_check_in',
                        /* 'Feedback Survey' => 'feedback_survey', */
                        'Badge Creation' => 'badge_creation',
                        'Attendees Info' => 'attendees_info',
                        /* 'Interactive Tools' => 'interactive_tools',*/
                    ];
                ?>
                <?php $__currentLoopData = $header_toggles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $header_toggle => $id): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="d-inline-flex form-check form-switch">
                        <label class="form-check-label" for="<?php echo e($id); ?>"><?php echo e($header_toggle); ?></label>
                        <input class="form-check-input ms-2" type="checkbox" id="<?php echo e($id); ?>"
                            name="<?php echo e($id); ?>" <?php if((isset($event) && $event->$id == 1) || (isset($eventClone) && $eventClone->$id == 1) || request()->is('admin/new-event')): ?> checked <?php endif; ?>>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    <?php endif; ?>
    <hr class="my-3">
</div>
<?php /**PATH C:\laragon\www\bd-qr-badge\resources\views/layouts/event_details.blade.php ENDPATH**/ ?>